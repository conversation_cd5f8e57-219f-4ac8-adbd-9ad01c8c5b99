#!/usr/bin/env python3
"""Debug script to test sync vs async database operations."""

import asyncio
import uuid
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from src.config.settings import settings
from src.core.models.general.user import User
from src.core.repositories.general.user_repository import UserRepository

async def debug_sync_async_operations():
    """Debug sync vs async database operations."""
    
    # Create sync engine and session
    sync_engine = create_engine(settings.DATABASE_URL)
    SyncSession = sessionmaker(bind=sync_engine)
    sync_session = SyncSession()
    
    # Create async engine and session
    async_db_url = settings.DATABASE_URL
    if "postgresql" in async_db_url and "asyncpg" not in async_db_url:
        async_db_url = async_db_url.replace("postgresql://", "postgresql+asyncpg://")
    
    async_engine = create_async_engine(async_db_url)
    AsyncSession = async_sessionmaker(bind=async_engine)
    async_session = AsyncSession()
    
    try:
        # Create a test user with sync session
        unique_id = str(uuid.uuid4())[:8]
        test_email = f"debug_test_{unique_id}@example.com"
        
        print(f"Creating user with email: {test_email}")
        
        # Create user with sync session
        user_data = {
            "name": f"Debug User {unique_id}",
            "email": test_email,
            "password_hash": "test_hash",
            "is_active": True,
        }
        
        sync_user = User(**user_data)
        sync_session.add(sync_user)
        sync_session.commit()
        
        print(f"User created with sync session, ID: {sync_user.id}")
        
        # Try to find the user with sync session
        sync_found = sync_session.query(User).filter(User.email == test_email).first()
        print(f"Sync session found user: {sync_found is not None}")
        if sync_found:
            print(f"  - ID: {sync_found.id}, Email: {sync_found.email}")
        
        # Try to find the user with async session
        async_repo = UserRepository(async_session)
        async_found = await async_repo.get_by_email(test_email)
        print(f"Async session found user: {async_found is not None}")
        if async_found:
            print(f"  - ID: {async_found.id}, Email: {async_found.email}")
        
        # Check raw SQL query with async session
        stmt = text("SELECT id, email FROM users WHERE email = :email")
        result = await async_session.execute(stmt, {"email": test_email})
        raw_result = result.fetchone()
        print(f"Raw async SQL found user: {raw_result is not None}")
        if raw_result:
            print(f"  - Raw result: {raw_result}")
            
    finally:
        # Cleanup
        sync_session.close()
        await async_session.close()
        sync_engine.dispose()
        await async_engine.dispose()

if __name__ == "__main__":
    asyncio.run(debug_sync_async_operations())
